#!/usr/bin/env python
# -*- coding: utf-8 -*-

import subprocess
import sys
import itertools

def test_password(rar_file, password):
    """使用unrar命令测试密码"""
    try:
        cmd = ['unrar', 't', rar_file, f'-p{password}']
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=5)
        return result.returncode == 0
    except:
        return False

def main():
    rar_file = "ziliao.rar"
    
    print("全面密码测试 - 多种LZW@解释...")
    
    passwords_to_test = []
    
    # 1. 可能LZW@是完整密码
    passwords_to_test.append("LZW@")
    
    # 2. 可能是姓名+@+其他信息的组合
    # 常见姓名拼音组合
    names = [
        "LiZhiWei", "LiZhengWei", "LiZhenWei", "LiZhiWen", "LiZhengWen", 
        "LiuZhiWei", "LiuZhengWei", "LiuZhenW<PERSON>", "LiuZhiWen", "LiuZhengWen",
        "LiZhiWang", "LiZhengWang", "LiZhenWang", "LiZhiWu", "LiZhengWu",
        "LuoZhiWei", "LuoZhengWei", "LuoZhenWei", "LuoZhiWen", "LuoZhengWen"
    ]
    
    # 3. 简化的姓名组合
    short_names = ["LZW", "lzw", "Lzw", "LZw", "lZW", "lZw", "LzW", "lzW"]
    
    # 4. 数字组合 (生日、年份、电话等)
    numbers = [
        "123", "456", "789", "000", "111", "222", "333", "444", "555", "666", "777", "888", "999",
        "1234", "5678", "9999", "0000", "1111", "2222", "8888",
        "12345", "54321", "11111", "88888", "99999",
        "123456", "654321", "111111", "888888", "999999",
        "19", "20", "21", "22", "23", "24", "25", "80", "85", "90", "95",
        "1980", "1985", "1990", "1995", "2000", "2020", "2021", "2022", "2023", "2024", "2025"
    ]
    
    # 5. 特殊符号
    symbols = ["@", "#", "$", "%", "&", "*", "!", "?", "+", "-", "_", ".", "~", "^"]
    
    # 6. 基本组合测试
    for name in short_names:
        passwords_to_test.append(name)
        passwords_to_test.append(f"{name}@")
        
        # name + @ + 数字
        for num in numbers[:20]:  # 限制数量
            passwords_to_test.append(f"{name}@{num}")
            passwords_to_test.append(f"{name}{num}@")
            passwords_to_test.append(f"{num}{name}@")
        
        # name + 其他符号 + 数字
        for symbol in symbols[:5]:
            for num in numbers[:10]:
                passwords_to_test.append(f"{name}{symbol}{num}")
    
    # 7. 长姓名组合
    for name in names[:5]:  # 限制数量
        passwords_to_test.append(f"{name}@")
        for num in numbers[:10]:
            passwords_to_test.append(f"{name}@{num}")
    
    # 8. 特殊模式
    special_patterns = [
        # 可能的电话号码
        "13800138000", "13888888888", "15000000000", "18888888888",
        # 可能的QQ号
        "123456789", "987654321", "111111111", "888888888",
        # 可能的身份证后几位
        "1234", "5678", "0123", "9876",
        # 可能的密码模式
        "password", "123456", "admin", "root", "user", "test"
    ]
    
    # 9. 组合特殊模式
    for pattern in special_patterns:
        passwords_to_test.append(f"LZW@{pattern}")
        passwords_to_test.append(f"lzw@{pattern}")
        passwords_to_test.append(f"{pattern}@LZW")
        passwords_to_test.append(f"{pattern}@lzw")
    
    # 10. 可能的完整密码（基于常见模式）
    full_passwords = [
        "LZW@123456", "LZW@654321", "LZW@111111", "LZW@888888",
        "lzw@123456", "lzw@654321", "lzw@111111", "lzw@888888",
        "LZW@password", "LZW@admin", "LZW@root",
        "LZW@2024", "LZW@2023", "LZW@2022", "LZW@2021", "LZW@2020",
        "LZW@1234567890", "LZW@0987654321"
    ]
    
    passwords_to_test.extend(full_passwords)
    
    # 去重
    passwords_to_test = list(set(passwords_to_test))
    
    print(f"生成了 {len(passwords_to_test)} 个候选密码")
    print("开始测试...")
    
    for i, password in enumerate(passwords_to_test, 1):
        if i % 100 == 0 or i <= 50:
            print(f"[{i:4d}/{len(passwords_to_test)}] 测试: {password}")
        
        if test_password(rar_file, password):
            print(f"\n🎉 找到密码: {password}")
            with open("found_password.txt", "w") as f:
                f.write(f"RAR文件: {rar_file}\n密码: {password}\n")
            return password
    
    print("\n❌ 全面测试未找到密码")
    print("建议：")
    print("1. 检查密码提示是否有其他解释")
    print("2. 考虑密码可能不是基于LZW@的")
    print("3. 尝试使用专业的密码破解工具")
    return None

if __name__ == "__main__":
    result = main()
    if result:
        print(f"密码: {result}")
        sys.exit(0)
    else:
        sys.exit(1)
