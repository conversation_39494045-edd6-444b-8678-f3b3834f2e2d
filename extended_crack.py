#!/usr/bin/env python
# -*- coding: utf-8 -*-

import subprocess
import sys
import itertools

def test_password(rar_file, password):
    """使用unrar命令测试密码"""
    try:
        cmd = ['unrar', 't', rar_file, f'-p{password}']
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=5)
        return result.returncode == 0
    except:
        return False

def main():
    rar_file = "ziliao.rar"
    
    print("扩展密码字典生成 - 基于LZW@提示...")
    
    passwords_to_test = []
    
    # 1. 直接测试LZW@相关的简单组合
    simple_tests = [
        "LZW@", "lzw@", "Lzw@", "LZW", "lzw", "Lzw",
        "LZW@123", "LZW@456", "LZW@789", "LZ<PERSON>@000", "LZ<PERSON>@111", "<PERSON>Z<PERSON>@222", "<PERSON><PERSON><PERSON>@333", "<PERSON><PERSON><PERSON>@444", "<PERSON><PERSON><PERSON>@555", "<PERSON><PERSON><PERSON>@666", "<PERSON><PERSON><PERSON>@777", "<PERSON>Z<PERSON>@888", "<PERSON><PERSON><PERSON>@999",
        "lzw@123", "lzw@456", "lzw@789", "lzw@000", "lzw@111", "lzw@222", "lzw@333", "lzw@444", "lzw@555", "lzw@666", "lzw@777", "lzw@888", "lzw@999",
        "Lzw@123", "Lzw@456", "Lzw@789", "Lzw@000", "Lzw@111", "Lzw@222", "Lzw@333", "Lzw@444", "Lzw@555", "Lzw@666", "Lzw@777", "Lzw@888", "Lzw@999"
    ]
    
    # 2. 年份组合
    years = ["2020", "2021", "2022", "2023", "2024", "2025"]
    for base in ["LZW@", "lzw@", "Lzw@"]:
        for year in years:
            passwords_to_test.append(f"{base}{year}")
    
    # 3. 生日可能组合
    months = ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "11", "12"]
    days = ["01", "02", "03", "04", "05", "06", "07", "08", "09", "10", "15", "20", "25", "30"]
    for base in ["LZW@", "lzw@", "Lzw@"]:
        for month in months[:6]:  # 只测试前6个月
            for day in days[:8]:  # 只测试前8天
                passwords_to_test.append(f"{base}{month}{day}")
    
    # 4. 电话号码模式
    phone_prefixes = ["138", "139", "150", "186", "187", "188"]
    for base in ["LZW@", "lzw@", "Lzw@"]:
        for prefix in phone_prefixes:
            passwords_to_test.append(f"{base}{prefix}")
            for suffix in ["0000", "1111", "8888"]:
                passwords_to_test.append(f"{base}{prefix}{suffix}")
    
    # 5. 特殊符号变化
    symbols = ["#", "$", "%", "&", "*", "!", "?", "+", "-", "_"]
    for symbol in symbols:
        for num in ["123", "456", "789", "000", "111", "666", "888", "999"]:
            passwords_to_test.append(f"LZW{symbol}{num}")
            passwords_to_test.append(f"lzw{symbol}{num}")
    
    # 6. 双符号组合
    for s1 in ["@", "#", "$"]:
        for s2 in ["@", "#", "$"]:
            if s1 != s2:
                passwords_to_test.append(f"LZW{s1}{s2}")
                passwords_to_test.append(f"LZW{s1}123{s2}")
    
    # 7. 常见密码模式
    common_patterns = [
        "LZW@2024", "LZW@2023", "LZW@1234", "LZW@12345", "LZW@123456",
        "LZW@qwerty", "LZW@password", "LZW@admin", "LZW@root",
        "LZW123@", "LZW456@", "LZW789@", "LZW000@", "LZW111@", "LZW888@",
        "123LZW@", "456LZW@", "789LZW@", "000LZW@", "111LZW@", "888LZW@",
        "LZW@abc", "LZW@ABC", "LZW@Abc",
        "LZW@520", "LZW@1314", "LZW@5201314"
    ]
    
    passwords_to_test.extend(simple_tests)
    passwords_to_test.extend(common_patterns)
    
    # 去重
    passwords_to_test = list(set(passwords_to_test))
    
    print(f"生成了 {len(passwords_to_test)} 个候选密码")
    print("开始测试...")
    
    for i, password in enumerate(passwords_to_test, 1):
        print(f"[{i:4d}/{len(passwords_to_test)}] 测试: {password}")
        
        if test_password(rar_file, password):
            print(f"\n🎉 找到密码: {password}")
            with open("found_password.txt", "w") as f:
                f.write(f"RAR文件: {rar_file}\n密码: {password}\n")
            return password
    
    print("\n❌ 扩展字典未找到密码")
    return None

if __name__ == "__main__":
    result = main()
    if result:
        print(f"密码: {result}")
        sys.exit(0)
    else:
        sys.exit(1)
