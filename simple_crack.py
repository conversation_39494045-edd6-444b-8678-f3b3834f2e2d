#!/usr/bin/env python
# -*- coding: utf-8 -*-

import subprocess
import sys
import time

def test_password(rar_file, password):
    """使用unrar命令测试密码"""
    try:
        # 使用unrar t命令测试密码
        cmd = ['unrar', 't', rar_file, f'-p{password}']
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)

        # 检查返回码，0表示成功
        if result.returncode == 0:
            return True
        return False
    except subprocess.TimeoutExpired:
        return False
    except Exception as e:
        print(f"测试密码 '{password}' 时出错: {e}")
        return False

def main():
    rar_file = "ziliao.rar"

    print(f"开始测试RAR文件: {rar_file}")

    # 测试常见密码
    common_passwords = [
        "123456", "password", "123123", "000000", "111111", "666666", "888888", "999999",
        "1234", "12345", "123456789", "qwerty", "abc123", "admin", "root", "test",
        "ziliao", "资料", "1111", "2222", "3333", "4444", "5555", "6666", "7777", "8888", "9999", "0000"
    ]

    print(f"测试 {len(common_passwords)} 个常见密码...")
    for i, password in enumerate(common_passwords, 1):
        print(f"[{i:2d}/{len(common_passwords)}] 测试密码: {password}")
        if test_password(rar_file, password):
            print(f"\n✅ 找到密码: {password}")
            with open("found_password.txt", "w") as f:
                f.write(f"RAR文件: {rar_file}\n密码: {password}\n")
            return password

    print("\n测试4位数字密码...")
    for i in range(10000):
        password = f"{i:04d}"
        if i % 1000 == 0:
            print(f"测试进度: {password}")
        if test_password(rar_file, password):
            print(f"\n✅ 找到密码: {password}")
            with open("found_password.txt", "w") as f:
                f.write(f"RAR文件: {rar_file}\n密码: {password}\n")
            return password

    print("\n测试6位数字密码...")
    for i in range(100000):
        password = f"{i:06d}"
        if i % 10000 == 0:
            print(f"测试进度: {password}")
        if test_password(rar_file, password):
            print(f"\n✅ 找到密码: {password}")
            with open("found_password.txt", "w") as f:
                f.write(f"RAR文件: {rar_file}\n密码: {password}\n")
            return password

    print("\n测试简单字母密码...")
    simple_words = ["a", "aa", "aaa", "aaaa", "b", "bb", "bbb", "bbbb", "c", "cc", "ccc", "cccc"]
    for password in simple_words:
        print(f"测试密码: {password}")
        if test_password(rar_file, password):
            print(f"\n✅ 找到密码: {password}")
            with open("found_password.txt", "w") as f:
                f.write(f"RAR文件: {rar_file}\n密码: {password}\n")
            return password

    print("\n❌ 未找到密码")
    return None

if __name__ == "__main__":
    result = main()
    if result:
        print(f"密码: {result}")
        sys.exit(0)
    else:
        sys.exit(1)
