#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简单的RAR密码测试脚本
"""

import rarfile
import sys

def test_password(rar_file, password):
    """测试单个密码"""
    try:
        rf = rarfile.RarFile(rar_file)
        # 尝试测试压缩包
        rf.testrar(pwd=password.encode('utf-8'))
        return True
    except rarfile.RarWrongPassword:
        return False
    except Exception as e:
        print(f"测试密码 '{password}' 时出错: {e}")
        return False

def main():
    rar_file = "ziliao.rar"
    
    # 测试一些常见密码
    common_passwords = [
        "123456",
        "password",
        "123123",
        "000000",
        "111111",
        "666666",
        "888888",
        "999999",
        "1234",
        "12345",
        "123456789",
        "qwerty",
        "abc123",
        "admin",
        "root",
        "test",
        "ziliao",
        "资料",
        "1111",
        "2222",
        "3333",
        "4444",
        "5555",
        "6666",
        "7777",
        "8888",
        "9999",
        "0000"
    ]
    
    print(f"开始测试RAR文件: {rar_file}")
    print(f"测试 {len(common_passwords)} 个常见密码...")
    
    for i, password in enumerate(common_passwords, 1):
        print(f"[{i:2d}/{len(common_passwords)}] 测试密码: {password}")
        if test_password(rar_file, password):
            print(f"\n✅ 找到密码: {password}")
            return password
    
    print("\n❌ 未找到密码，尝试数字组合...")
    
    # 测试4位数字密码
    for i in range(10000):
        password = f"{i:04d}"
        if i % 1000 == 0:
            print(f"测试进度: {password}")
        if test_password(rar_file, password):
            print(f"\n✅ 找到密码: {password}")
            return password
    
    print("\n❌ 未找到密码")
    return None

if __name__ == "__main__":
    result = main()
    if result:
        print(f"密码: {result}")
        sys.exit(0)
    else:
        sys.exit(1)
