#!/bin/bash

# RAR密码破解脚本 - 基于密码提示 LZW@ 的系统化破解
# 密码规则：姓名拼音+特殊符号，特殊符号2个以内，中间字母数字，可能有电话号码

echo "开始RAR密码破解..."
echo "目标文件: ziliao.rar"
echo "密码提示: LZW@"
echo "破解策略: 系统化GPU掩码破解"
echo ""

# 先停止之前的进程
pkill -f "python main.py" 2>/dev/null || true

echo "=== 方法1: 6位数字密码 (可能是电话号码后6位) ==="
python main.py --rar_file ziliao.rar --mask "?d?d?d?d?d?d" --gpu 0 --threads_per_block 1024 --batch_size 100000

echo "=== 方法2: 8位数字密码 (可能是完整生日) ==="
python main.py --rar_file ziliao.rar --mask "?d?d?d?d?d?d?d?d" --gpu 0 --threads_per_block 1024 --batch_size 100000

echo "=== 方法3: 3字母+@+3数字 ==="
python main.py --rar_file ziliao.rar --mask "?l?l?l@?d?d?d" --gpu 0 --threads_per_block 1024 --batch_size 100000

echo "=== 方法4: 3字母+@+6数字 ==="
python main.py --rar_file ziliao.rar --mask "?l?l?l@?d?d?d?d?d?d" --gpu 0 --threads_per_block 1024 --batch_size 100000

echo "=== 方法5: 3大写字母+@+数字 ==="
python main.py --rar_file ziliao.rar --mask "?u?u?u@?d?d?d" --gpu 0 --threads_per_block 1024 --batch_size 100000

echo "=== 方法6: 混合大小写+@+数字 ==="
python main.py --rar_file ziliao.rar --mask "?u?l?u@?d?d?d" --gpu 0 --threads_per_block 1024 --batch_size 100000

echo "=== 方法4: 创建个性化密码字典 ==="
cat > custom_crack.py << 'EOF'
#!/usr/bin/env python
# -*- coding: utf-8 -*-

import subprocess
import sys
import itertools

def test_password(rar_file, password):
    """使用unrar命令测试密码"""
    try:
        cmd = ['unrar', 't', rar_file, f'-p{password}']
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=5)
        return result.returncode == 0
    except:
        return False

def main():
    rar_file = "ziliao.rar"

    print("基于LZW@提示生成个性化密码字典...")

    # 基础模式
    base_patterns = ["LZW", "lzw", "Lzw"]

    # 特殊符号 (不超过3个，常见2个以内)
    symbols = ["@", "#", "$", "%", "&", "*", "!", "?", "+", "-", "_", ".", "~"]

    # 数字组合
    numbers = ["123", "456", "789", "000", "111", "666", "888", "999", "520", "1314"]

    # 电话号码常见模式
    phone_patterns = ["138", "139", "150", "151", "152", "158", "159", "186", "187", "188"]

    passwords_to_test = []

    # 1. LZW@ + 数字组合
    for base in base_patterns:
        for num in numbers:
            passwords_to_test.append(f"{base}@{num}")
            passwords_to_test.append(f"{base}#{num}")
            passwords_to_test.append(f"{base}${num}")

    # 2. LZW + 符号 + 4位数字
    for base in base_patterns:
        for symbol in symbols[:5]:  # 只用前5个常见符号
            for i in range(1000, 10000, 111):  # 采样测试
                passwords_to_test.append(f"{base}{symbol}{i}")

    # 3. LZW + 电话号码模式
    for base in base_patterns:
        for phone in phone_patterns:
            for suffix in ["0000", "1111", "2222", "8888"]:
                passwords_to_test.append(f"{base}@{phone}{suffix}")
                passwords_to_test.append(f"{base}{phone}")

    # 4. 特殊组合
    special_combinations = [
        "LZW@123", "LZW@456", "LZW@789", "LZW@000", "LZW@111", "LZW@666", "LZW@888", "LZW@999",
        "lzw@123", "lzw@456", "lzw@789", "lzw@000", "lzw@111", "lzw@666", "lzw@888", "lzw@999",
        "LZW#123", "LZW$123", "LZW&123", "LZW*123", "LZW!123",
        "LZW@520", "LZW@1314", "LZW@2023", "LZW@2024", "LZW@2025",
        "LZW123@", "LZW456@", "LZW789@", "LZW888@",
        "LZW@12345", "LZW@123456", "LZW@654321",
        "LZW@@123", "LZW##123", "LZW$$123"
    ]

    passwords_to_test.extend(special_combinations)

    # 去重
    passwords_to_test = list(set(passwords_to_test))

    print(f"生成了 {len(passwords_to_test)} 个候选密码")
    print("开始测试...")

    for i, password in enumerate(passwords_to_test, 1):
        if i % 50 == 0:
            print(f"进度: {i}/{len(passwords_to_test)} - 当前测试: {password}")

        if test_password(rar_file, password):
            print(f"\n🎉 找到密码: {password}")
            with open("found_password.txt", "w") as f:
                f.write(f"RAR文件: {rar_file}\n密码: {password}\n时间: $(date)\n")
            return password

    print("\n❌ 个性化字典未找到密码")
    return None

if __name__ == "__main__":
    result = main()
    if result:
        print(f"密码: {result}")
        sys.exit(0)
    else:
        sys.exit(1)
EOF

echo "运行个性化密码字典破解..."
python custom_crack.py

echo "破解完成！"

